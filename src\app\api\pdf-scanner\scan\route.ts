import { NextRequest, NextResponse } from 'next/server';
import pdf from 'pdf-parse';

interface EmbeddedData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: string;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const pdfFile = formData.get('pdf') as File;

    if (!pdfFile) {
      return NextResponse.json(
        { success: false, error: 'PDF file is required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (pdfFile.type !== 'application/pdf') {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only PDF files are supported' },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (pdfFile.size > maxSize) {
      return NextResponse.json(
        { success: false, error: 'File size too large. Maximum size is 10MB' },
        { status: 400 }
      );
    }

    try {
      // Convert file to buffer
      const arrayBuffer = await pdfFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Extract text from PDF
      const pdfData = await pdf(buffer);
      const extractedText = pdfData.text;

      // Look for embedded data markers
      const embeddedDataRegex = /EMBEDDED_DATA_START:([\s\S]*?):EMBEDDED_DATA_END/;
      const match = extractedText.match(embeddedDataRegex);

      if (!match) {
        // Try alternative methods to find JSON data
        // Look for JSON-like patterns in the text
        const jsonPatterns = [
          // Look for complete JSON objects with templateId
          /"templateId"\s*:\s*"[^"]+"/g,
          // Look for userData objects
          /"userData"\s*:\s*\{[^}]+\}/g,
        ];

        let foundJsonData = null;
        for (const pattern of jsonPatterns) {
          const matches = extractedText.match(pattern);
          if (matches) {
            // Try to extract a larger JSON context around the match
            const matchIndex = extractedText.indexOf(matches[0]);
            const contextStart = Math.max(0, matchIndex - 500);
            const contextEnd = Math.min(extractedText.length, matchIndex + 2000);
            const context = extractedText.substring(contextStart, contextEnd);
            
            // Try to find a complete JSON object in the context
            const jsonStart = context.indexOf('{');
            const jsonEnd = context.lastIndexOf('}');
            if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
              const potentialJson = context.substring(jsonStart, jsonEnd + 1);
              try {
                const parsed = JSON.parse(potentialJson);
                if (parsed.templateId && parsed.userData) {
                  foundJsonData = parsed;
                  break;
                }
              } catch {
                // Continue searching
              }
            }
          }
        }

        if (!foundJsonData) {
          return NextResponse.json({
            success: false,
            error: 'No embedded template data found in this PDF',
            rawText: extractedText.substring(0, 1000) + (extractedText.length > 1000 ? '...' : '')
          });
        }

        // Use the found JSON data
        const embeddedData: EmbeddedData = foundJsonData;
        return NextResponse.json({
          success: true,
          data: embeddedData
        });
      }

      // Parse the embedded JSON data
      const jsonString = match[1];
      let embeddedData: EmbeddedData;

      try {
        embeddedData = JSON.parse(jsonString);
      } catch (parseError) {
        console.error('Error parsing embedded JSON:', parseError);
        return NextResponse.json({
          success: false,
          error: 'Found embedded data but failed to parse JSON',
          rawText: jsonString.substring(0, 500) + (jsonString.length > 500 ? '...' : '')
        });
      }

      // Validate the structure of embedded data
      if (!embeddedData.templateId || !embeddedData.userData) {
        return NextResponse.json({
          success: false,
          error: 'Embedded data is missing required fields (templateId, userData)',
          rawText: JSON.stringify(embeddedData, null, 2)
        });
      }

      // Return the successfully parsed data
      return NextResponse.json({
        success: true,
        data: embeddedData
      });

    } catch (pdfError) {
      console.error('Error processing PDF:', pdfError);
      return NextResponse.json({
        success: false,
        error: 'Failed to extract text from PDF. The file may be corrupted or password-protected.'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in PDF scanner:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error while processing PDF'
    }, { status: 500 });
  }
}
