"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, Search, AlertCircle, CheckCircle, Copy } from "lucide-react";
import { toast } from "sonner";

interface EmbeddedData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: string;
}

interface ScanResult {
  success: boolean;
  data?: EmbeddedData;
  error?: string;
  rawText?: string;
}

export default function PDFScannerPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [showRawText, setShowRawText] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast.error("File size too large. Maximum size is 10MB");
        return;
      }

      setSelectedFile(file);
      setScanResult(null);
      toast.success("PDF file selected successfully");
    }
  };

  const handleScanPDF = async () => {
    if (!selectedFile) {
      toast.error("Please select a PDF file first");
      return;
    }

    setIsScanning(true);
    setScanResult(null);

    try {
      const formData = new FormData();
      formData.append("pdf", selectedFile);

      const response = await fetch("/api/pdf-scanner/scan", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to scan PDF");
      }

      const result: ScanResult = await response.json();
      setScanResult(result);

      if (result.success) {
        toast.success("PDF scanned successfully! Embedded data found.");
      } else {
        toast.error(result.error || "No embedded data found in PDF");
      }
    } catch (error) {
      console.error("Error scanning PDF:", error);
      toast.error("Failed to scan PDF");
      setScanResult({
        success: false,
        error: "Failed to scan PDF. Please try again.",
      });
    } finally {
      setIsScanning(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            PDF Scanner
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Upload a PDF file to scan for embedded template data
          </p>
        </div>

        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload PDF File
            </CardTitle>
            <CardDescription>
              Select a PDF file generated by the LDIS Template System to scan for embedded data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="pdf-file">PDF File</Label>
              <Input
                id="pdf-file"
                type="file"
                accept=".pdf,application/pdf"
                onChange={handleFileChange}
                className="mt-1"
              />
            </div>

            {selectedFile && (
              <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <FileText className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-sm text-green-800 dark:text-green-200">
                  {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
            )}

            <Button
              onClick={handleScanPDF}
              disabled={!selectedFile || isScanning}
              className="w-full"
            >
              {isScanning ? (
                <>
                  <Search className="h-4 w-4 mr-2 animate-spin" />
                  Scanning PDF...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Scan PDF
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Results Section */}
        {scanResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {scanResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                Scan Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              {scanResult.success && scanResult.data ? (
                <div className="space-y-6">
                  {/* Template Information */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Template Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Template ID
                        </Label>
                        <div className="flex items-center gap-2">
                          <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                            {scanResult.data.templateId}
                          </p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(scanResult.data!.templateId)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Template Name
                        </Label>
                        <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                          {scanResult.data.templateName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Layout Size
                        </Label>
                        <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                          {scanResult.data.layoutSize}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Generated At
                        </Label>
                        <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                          {formatDate(scanResult.data.generatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* User Data */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">User Data</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(scanResult.data.userData).map(([key, value]) => (
                        <div key={key}>
                          <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {key}
                          </Label>
                          <div className="flex items-center gap-2">
                            <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded flex-1">
                              {value || "(empty)"}
                            </p>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyToClipboard(value)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Photo Information */}
                  {scanResult.data.photoBase64 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Photo Information</h3>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Photo found (Base64 encoded, {scanResult.data.photoBase64.length} characters)
                        </p>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(scanResult.data!.photoBase64!)}
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy Base64
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Placeholders */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Template Placeholders</h3>
                    <div className="flex flex-wrap gap-2">
                      {scanResult.data.placeholders.map((placeholder, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                        >
                          {placeholder}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Raw JSON */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-semibold">Raw JSON Data</h3>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(JSON.stringify(scanResult.data, null, 2))}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy JSON
                      </Button>
                    </div>
                    <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto max-h-64">
                      {JSON.stringify(scanResult.data, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    No Embedded Data Found
                  </h3>
                  <p className="text-red-600 dark:text-red-400 mb-4">
                    {scanResult.error || "This PDF doesn't contain embedded template data."}
                  </p>
                  
                  {scanResult.rawText && (
                    <div className="mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowRawText(!showRawText)}
                      >
                        {showRawText ? "Hide" : "Show"} Raw Text
                      </Button>
                      
                      {showRawText && (
                        <div className="mt-4">
                          <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto max-h-64 text-left">
                            {scanResult.rawText}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
