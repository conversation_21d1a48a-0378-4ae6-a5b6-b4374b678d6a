"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Search, CheckCircle, Copy } from "lucide-react";
import { toast } from "sonner";

const sampleEmbeddedData = {
  templateId: "good-moral-certificate",
  templateName: "Good Moral Certificate",
  placeholders: ["[AGE]", "[BARANGAY]", "[CTC NUMBER]", "[DAY]", "[FIRST NAME]", "[LAST NAME]", "[MIDDLE INITIAL]", "[MONTH]", "[O.R. NUMBER]", "[SUFF<PERSON>]", "[TIN NUMBER]", "[YEAR]"],
  userData: {
    "FIRST NAME": "John",
    "LAST NAME": "Doe",
    "MIDDLE INITIAL": "M.",
    "AGE": "25",
    "BARANGAY": "Sample Barangay",
    "CTC NUMBER": "12345678",
    "DAY": "15",
    "MONTH": "January",
    "YEAR": "2024",
    "O.R. NUMBER": "OR-2024-001",
    "TIN NUMBER": "123-456-789-000",
    "SUFFIX": ""
  },
  photoBase64: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
  generatedAt: "2024-01-24T10:30:00.000Z",
  layoutSize: "A4"
};

export default function PDFScannerDemoPage() {
  const [showDemo, setShowDemo] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            PDF Scanner Demo
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            This demonstrates how the PDF scanner extracts embedded data from generated PDFs
          </p>
        </div>

        {/* Demo Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sample Embedded Data
            </CardTitle>
            <CardDescription>
              This is what the PDF scanner would extract from a PDF generated by the LDIS Template System
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={() => setShowDemo(!showDemo)}
              className="w-full"
            >
              {showDemo ? (
                <>
                  Hide Demo Data
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Show Sample Extracted Data
                </>
              )}
            </Button>

            {showDemo && (
              <div className="space-y-6 mt-6">
                {/* Template Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Template Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Template ID
                      </label>
                      <div className="flex items-center gap-2">
                        <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded font-mono">
                          {sampleEmbeddedData.templateId}
                        </p>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(sampleEmbeddedData.templateId)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Template Name
                      </label>
                      <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                        {sampleEmbeddedData.templateName}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Layout Size
                      </label>
                      <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                        {sampleEmbeddedData.layoutSize}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Generated At
                      </label>
                      <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                        {formatDate(sampleEmbeddedData.generatedAt)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* User Data */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">User Data</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(sampleEmbeddedData.userData).map(([key, value]) => (
                      <div key={key}>
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {key}
                        </label>
                        <div className="flex items-center gap-2">
                          <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded flex-1">
                            {value || "(empty)"}
                          </p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(value)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Photo Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Photo Information</h3>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Photo found (Base64 encoded, {sampleEmbeddedData.photoBase64.length} characters)
                    </p>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(sampleEmbeddedData.photoBase64)}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy Base64
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Placeholders */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Template Placeholders</h3>
                  <div className="flex flex-wrap gap-2">
                    {sampleEmbeddedData.placeholders.map((placeholder, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                      >
                        {placeholder}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Raw JSON */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold">Raw JSON Data</h3>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(JSON.stringify(sampleEmbeddedData, null, 2))}
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy JSON
                    </Button>
                  </div>
                  <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto max-h-64">
                    {JSON.stringify(sampleEmbeddedData, null, 2)}
                  </pre>
                </div>

                {/* Success Message */}
                <div className="flex items-center gap-2 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <div>
                    <p className="font-medium text-green-800 dark:text-green-200">
                      Data Successfully Extracted!
                    </p>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      This data can now be reused to pre-populate forms or for data analysis.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How It Works</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full flex items-center justify-center text-sm font-semibold">
                  1
                </div>
                <div>
                  <h4 className="font-medium">Generate PDF with Embedded Data</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    When you generate a PDF using the LDIS Template System, all user input and photos are embedded as invisible text in the PDF.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full flex items-center justify-center text-sm font-semibold">
                  2
                </div>
                <div>
                  <h4 className="font-medium">Upload PDF to Scanner</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Upload the generated PDF to the scanner page. The system extracts all text content from the PDF.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full flex items-center justify-center text-sm font-semibold">
                  3
                </div>
                <div>
                  <h4 className="font-medium">Extract and Display Data</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    The scanner finds the embedded JSON data and displays it in a user-friendly format, ready for reuse.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
