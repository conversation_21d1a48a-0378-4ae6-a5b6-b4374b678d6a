import { NextRequest, NextResponse } from 'next/server';
import { UserModel } from '@/lib/auth';
import { initDatabase } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    await initDatabase();
    
    const sessionCookie = request.cookies.get('admin-session');
    
    if (!sessionCookie) {
      return NextResponse.json(
        { success: false, message: 'No session found' },
        { status: 401 }
      );
    }

    try {
      // Decode the session token
      const decoded = Buffer.from(sessionCookie.value, 'base64').toString();
      const [username, timestamp] = decoded.split(':');
      
      // Check if session is expired (24 hours)
      const sessionTime = parseInt(timestamp);
      const now = Date.now();
      const sessionAge = now - sessionTime;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (sessionAge > maxAge) {
        return NextResponse.json(
          { success: false, message: 'Session expired' },
          { status: 401 }
        );
      }

      // Verify user still exists
      const user = await UserModel.findByUsername(username);
      if (!user) {
        return NextResponse.json(
          { success: false, message: 'User not found' },
          { status: 401 }
        );
      }

      return NextResponse.json({
        success: true,
        user: { username: user.username },
        sessionAge: Math.floor(sessionAge / 1000) // in seconds
      });
    } catch (decodeError) {
      return NextResponse.json(
        { success: false, message: 'Invalid session' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Session API error:', error);
    return NextResponse.json(
      { success: false, message: 'Session check failed' },
      { status: 500 }
    );
  }
}
